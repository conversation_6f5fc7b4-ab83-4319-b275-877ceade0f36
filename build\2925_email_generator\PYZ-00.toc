('C:\\Users\\<USER>\\Desktop\\新建文件夹\\build\\2925_email_generator\\PYZ-00.pyz',
 [('OpenSSL',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('PIL',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'c:\\programdata\\anaconda3\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'c:\\programdata\\anaconda3\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'c:\\programdata\\anaconda3\\lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\programdata\\anaconda3\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_markupbase',
   'c:\\programdata\\anaconda3\\lib\\_markupbase.py',
   'PYMODULE'),
  ('_osx_support',
   'c:\\programdata\\anaconda3\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc', 'c:\\programdata\\anaconda3\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'c:\\programdata\\anaconda3\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'c:\\programdata\\anaconda3\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'c:\\programdata\\anaconda3\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'c:\\programdata\\anaconda3\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'c:\\programdata\\anaconda3\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'c:\\programdata\\anaconda3\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\programdata\\anaconda3\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'c:\\programdata\\anaconda3\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'c:\\programdata\\anaconda3\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'c:\\programdata\\anaconda3\\lib\\bisect.py', 'PYMODULE'),
  ('brotli',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\brotli\\__init__.py',
   'PYMODULE'),
  ('brotli.brotli',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\brotli\\brotli.py',
   'PYMODULE'),
  ('bs4',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.builder',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.dammit',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'c:\\programdata\\anaconda3\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'c:\\programdata\\anaconda3\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.api',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'c:\\programdata\\anaconda3\\lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('cmd', 'c:\\programdata\\anaconda3\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'c:\\programdata\\anaconda3\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'c:\\programdata\\anaconda3\\lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'c:\\programdata\\anaconda3\\lib\\colorsys.py', 'PYMODULE'),
  ('commctrl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\programdata\\anaconda3\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\programdata\\anaconda3\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\programdata\\anaconda3\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\programdata\\anaconda3\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\programdata\\anaconda3\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'c:\\programdata\\anaconda3\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'c:\\programdata\\anaconda3\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'c:\\programdata\\anaconda3\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'c:\\programdata\\anaconda3\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._der',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\_der.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.interfaces',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\interfaces.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dh',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dsa',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed25519',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed448',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.encode_asn1',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\encode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hashes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hmac',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ocsp',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ocsp.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.poly1305',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\poly1305.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x25519',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x448',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x509',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x509.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.scrypt',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\scrypt.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.ocsp',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\x509\\ocsp.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('csv', 'c:\\programdata\\anaconda3\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'c:\\programdata\\anaconda3\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'c:\\programdata\\anaconda3\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'c:\\programdata\\anaconda3\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\programdata\\anaconda3\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\programdata\\anaconda3\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\programdata\\anaconda3\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\programdata\\anaconda3\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'c:\\programdata\\anaconda3\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'c:\\programdata\\anaconda3\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('customtkinter',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_input_dialog',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\ctk_input_dialog.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_tk',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\ctk_tk.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_toplevel',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\ctk_toplevel.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_base_class',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_tracker',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_tracker.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.ctk_canvas',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\ctk_canvas.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.draw_engine',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\draw_engine.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.ctk_base_class',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\ctk_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.dropdown_menu',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\dropdown_menu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_button',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_checkbox',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_checkbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_combobox',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_combobox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_entry',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_entry.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_frame',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_label',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_label.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_optionmenu',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_optionmenu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_progressbar',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_progressbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_radiobutton',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_radiobutton.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollable_frame',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollable_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollbar',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_segmented_button',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_segmented_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_slider',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_slider.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_switch',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_switch.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_tabview',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_tabview.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_textbox',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_textbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\font\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.ctk_font',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\font\\ctk_font.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.font_manager',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\font\\font_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\image\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image.ctk_image',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\image\\ctk_image.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_base_class',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_tracker',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_tracker.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\theme\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme.theme_manager',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\theme\\theme_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\utility\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility.utility_functions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\customtkinter\\windows\\widgets\\utility\\utility_functions.py',
   'PYMODULE'),
  ('darkdetect',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\darkdetect\\__init__.py',
   'PYMODULE'),
  ('darkdetect._dummy',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\darkdetect\\_dummy.py',
   'PYMODULE'),
  ('darkdetect._linux_detect',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\darkdetect\\_linux_detect.py',
   'PYMODULE'),
  ('darkdetect._mac_detect',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\darkdetect\\_mac_detect.py',
   'PYMODULE'),
  ('darkdetect._windows_detect',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\darkdetect\\_windows_detect.py',
   'PYMODULE'),
  ('dataclasses',
   'c:\\programdata\\anaconda3\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'c:\\programdata\\anaconda3\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'c:\\programdata\\anaconda3\\lib\\decimal.py', 'PYMODULE'),
  ('defusedxml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'c:\\programdata\\anaconda3\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'c:\\programdata\\anaconda3\\lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'c:\\programdata\\anaconda3\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'c:\\programdata\\anaconda3\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'c:\\programdata\\anaconda3\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'c:\\programdata\\anaconda3\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'c:\\programdata\\anaconda3\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'c:\\programdata\\anaconda3\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'c:\\programdata\\anaconda3\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'c:\\programdata\\anaconda3\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'c:\\programdata\\anaconda3\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'c:\\programdata\\anaconda3\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'c:\\programdata\\anaconda3\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'c:\\programdata\\anaconda3\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'c:\\programdata\\anaconda3\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'c:\\programdata\\anaconda3\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'c:\\programdata\\anaconda3\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'c:\\programdata\\anaconda3\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'c:\\programdata\\anaconda3\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'c:\\programdata\\anaconda3\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'c:\\programdata\\anaconda3\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'c:\\programdata\\anaconda3\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'c:\\programdata\\anaconda3\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'c:\\programdata\\anaconda3\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'c:\\programdata\\anaconda3\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'c:\\programdata\\anaconda3\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'c:\\programdata\\anaconda3\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'c:\\programdata\\anaconda3\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'c:\\programdata\\anaconda3\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.util',
   'c:\\programdata\\anaconda3\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'c:\\programdata\\anaconda3\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'c:\\programdata\\anaconda3\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('docutils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\__init__.py',
   'PYMODULE'),
  ('docutils.frontend',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\frontend.py',
   'PYMODULE'),
  ('docutils.io',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\io.py',
   'PYMODULE'),
  ('docutils.languages',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\__init__.py',
   'PYMODULE'),
  ('docutils.languages.af',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\af.py',
   'PYMODULE'),
  ('docutils.languages.ca',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\ca.py',
   'PYMODULE'),
  ('docutils.languages.cs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\cs.py',
   'PYMODULE'),
  ('docutils.languages.da',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\da.py',
   'PYMODULE'),
  ('docutils.languages.de',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\de.py',
   'PYMODULE'),
  ('docutils.languages.en',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\en.py',
   'PYMODULE'),
  ('docutils.languages.eo',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\eo.py',
   'PYMODULE'),
  ('docutils.languages.es',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\es.py',
   'PYMODULE'),
  ('docutils.languages.fa',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\fa.py',
   'PYMODULE'),
  ('docutils.languages.fi',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\fi.py',
   'PYMODULE'),
  ('docutils.languages.fr',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\fr.py',
   'PYMODULE'),
  ('docutils.languages.gl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\gl.py',
   'PYMODULE'),
  ('docutils.languages.he',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\he.py',
   'PYMODULE'),
  ('docutils.languages.it',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\it.py',
   'PYMODULE'),
  ('docutils.languages.ja',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\ja.py',
   'PYMODULE'),
  ('docutils.languages.ko',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\ko.py',
   'PYMODULE'),
  ('docutils.languages.lt',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\lt.py',
   'PYMODULE'),
  ('docutils.languages.lv',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\lv.py',
   'PYMODULE'),
  ('docutils.languages.nl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\nl.py',
   'PYMODULE'),
  ('docutils.languages.pl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\pl.py',
   'PYMODULE'),
  ('docutils.languages.pt_br',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\pt_br.py',
   'PYMODULE'),
  ('docutils.languages.ru',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\ru.py',
   'PYMODULE'),
  ('docutils.languages.sk',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\sk.py',
   'PYMODULE'),
  ('docutils.languages.sv',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\sv.py',
   'PYMODULE'),
  ('docutils.languages.zh_cn',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\zh_cn.py',
   'PYMODULE'),
  ('docutils.languages.zh_tw',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\languages\\zh_tw.py',
   'PYMODULE'),
  ('docutils.nodes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\nodes.py',
   'PYMODULE'),
  ('docutils.parsers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\directives\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.admonitions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\directives\\admonitions.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.body',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\directives\\body.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.html',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\directives\\html.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.images',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\directives\\images.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.misc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\directives\\misc.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.parts',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\directives\\parts.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.references',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\directives\\references.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.tables',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\directives\\tables.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.af',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\af.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ca',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\ca.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.cs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\cs.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.da',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\da.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.de',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\de.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.en',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\en.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.eo',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\eo.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.es',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\es.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.fa',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\fa.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.fi',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\fi.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.fr',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\fr.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.gl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\gl.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.he',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\he.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.it',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\it.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ja',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\ja.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ko',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\ko.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.lt',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\lt.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.lv',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\lv.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.nl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\nl.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.pl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\pl.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.pt_br',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\pt_br.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ru',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\ru.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.sk',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\sk.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.sv',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\sv.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.zh_cn',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\zh_cn.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.zh_tw',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\languages\\zh_tw.py',
   'PYMODULE'),
  ('docutils.parsers.rst.roles',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\roles.py',
   'PYMODULE'),
  ('docutils.parsers.rst.states',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\states.py',
   'PYMODULE'),
  ('docutils.parsers.rst.tableparser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\parsers\\rst\\tableparser.py',
   'PYMODULE'),
  ('docutils.readers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\readers\\__init__.py',
   'PYMODULE'),
  ('docutils.readers.standalone',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\readers\\standalone.py',
   'PYMODULE'),
  ('docutils.statemachine',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\statemachine.py',
   'PYMODULE'),
  ('docutils.transforms',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\transforms\\__init__.py',
   'PYMODULE'),
  ('docutils.transforms.components',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\transforms\\components.py',
   'PYMODULE'),
  ('docutils.transforms.frontmatter',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\transforms\\frontmatter.py',
   'PYMODULE'),
  ('docutils.transforms.misc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\transforms\\misc.py',
   'PYMODULE'),
  ('docutils.transforms.parts',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\transforms\\parts.py',
   'PYMODULE'),
  ('docutils.transforms.references',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\transforms\\references.py',
   'PYMODULE'),
  ('docutils.transforms.universal',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\transforms\\universal.py',
   'PYMODULE'),
  ('docutils.transforms.writer_aux',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\transforms\\writer_aux.py',
   'PYMODULE'),
  ('docutils.utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\__init__.py',
   'PYMODULE'),
  ('docutils.utils.code_analyzer',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\code_analyzer.py',
   'PYMODULE'),
  ('docutils.utils.error_reporting',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\error_reporting.py',
   'PYMODULE'),
  ('docutils.utils.math',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\math\\__init__.py',
   'PYMODULE'),
  ('docutils.utils.math.latex2mathml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\math\\latex2mathml.py',
   'PYMODULE'),
  ('docutils.utils.math.math2html',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\math\\math2html.py',
   'PYMODULE'),
  ('docutils.utils.math.tex2mathml_extern',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\math\\tex2mathml_extern.py',
   'PYMODULE'),
  ('docutils.utils.math.tex2unichar',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\math\\tex2unichar.py',
   'PYMODULE'),
  ('docutils.utils.math.unichar2tex',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\math\\unichar2tex.py',
   'PYMODULE'),
  ('docutils.utils.punctuation_chars',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\punctuation_chars.py',
   'PYMODULE'),
  ('docutils.utils.roman',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\roman.py',
   'PYMODULE'),
  ('docutils.utils.smartquotes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\smartquotes.py',
   'PYMODULE'),
  ('docutils.utils.urischemes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\utils\\urischemes.py',
   'PYMODULE'),
  ('docutils.writers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\__init__.py',
   'PYMODULE'),
  ('docutils.writers._html_base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\_html_base.py',
   'PYMODULE'),
  ('docutils.writers.docutils_xml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\docutils_xml.py',
   'PYMODULE'),
  ('docutils.writers.html4css1',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\html4css1\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.html5_polyglot',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\html5_polyglot\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.latex2e',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\latex2e\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.manpage',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\manpage.py',
   'PYMODULE'),
  ('docutils.writers.null',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\null.py',
   'PYMODULE'),
  ('docutils.writers.odf_odt',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\odf_odt\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.odf_odt.pygmentsformatter',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\odf_odt\\pygmentsformatter.py',
   'PYMODULE'),
  ('docutils.writers.pep_html',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\pep_html\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.pseudoxml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\pseudoxml.py',
   'PYMODULE'),
  ('docutils.writers.s5_html',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\s5_html\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.xetex',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\docutils\\writers\\xetex\\__init__.py',
   'PYMODULE'),
  ('dummy_threading',
   'c:\\programdata\\anaconda3\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('email', 'c:\\programdata\\anaconda3\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'c:\\programdata\\anaconda3\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\programdata\\anaconda3\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\programdata\\anaconda3\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\programdata\\anaconda3\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\programdata\\anaconda3\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\programdata\\anaconda3\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\programdata\\anaconda3\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\programdata\\anaconda3\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\programdata\\anaconda3\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\programdata\\anaconda3\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\programdata\\anaconda3\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'c:\\programdata\\anaconda3\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\programdata\\anaconda3\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\programdata\\anaconda3\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'c:\\programdata\\anaconda3\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\programdata\\anaconda3\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\programdata\\anaconda3\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\programdata\\anaconda3\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\programdata\\anaconda3\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fractions', 'c:\\programdata\\anaconda3\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'c:\\programdata\\anaconda3\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'c:\\programdata\\anaconda3\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'c:\\programdata\\anaconda3\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'c:\\programdata\\anaconda3\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'c:\\programdata\\anaconda3\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'c:\\programdata\\anaconda3\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'c:\\programdata\\anaconda3\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'c:\\programdata\\anaconda3\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'c:\\programdata\\anaconda3\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'c:\\programdata\\anaconda3\\lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'c:\\programdata\\anaconda3\\lib\\html\\parser.py',
   'PYMODULE'),
  ('html5lib',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\__init__.py',
   'PYMODULE'),
  ('html5lib._ihatexml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\_ihatexml.py',
   'PYMODULE'),
  ('html5lib._inputstream',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\_inputstream.py',
   'PYMODULE'),
  ('html5lib._tokenizer',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\_tokenizer.py',
   'PYMODULE'),
  ('html5lib._trie',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\_trie\\__init__.py',
   'PYMODULE'),
  ('html5lib._trie._base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\_trie\\_base.py',
   'PYMODULE'),
  ('html5lib._trie.py',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\_trie\\py.py',
   'PYMODULE'),
  ('html5lib._utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\_utils.py',
   'PYMODULE'),
  ('html5lib.constants',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\constants.py',
   'PYMODULE'),
  ('html5lib.filters',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\filters\\__init__.py',
   'PYMODULE'),
  ('html5lib.filters.alphabeticalattributes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\filters\\alphabeticalattributes.py',
   'PYMODULE'),
  ('html5lib.filters.base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\filters\\base.py',
   'PYMODULE'),
  ('html5lib.filters.inject_meta_charset',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\filters\\inject_meta_charset.py',
   'PYMODULE'),
  ('html5lib.filters.optionaltags',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\filters\\optionaltags.py',
   'PYMODULE'),
  ('html5lib.filters.sanitizer',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\filters\\sanitizer.py',
   'PYMODULE'),
  ('html5lib.filters.whitespace',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\filters\\whitespace.py',
   'PYMODULE'),
  ('html5lib.html5parser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\html5parser.py',
   'PYMODULE'),
  ('html5lib.serializer',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\serializer.py',
   'PYMODULE'),
  ('html5lib.treebuilders',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\treebuilders\\__init__.py',
   'PYMODULE'),
  ('html5lib.treebuilders.base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\treebuilders\\base.py',
   'PYMODULE'),
  ('html5lib.treebuilders.dom',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\treebuilders\\dom.py',
   'PYMODULE'),
  ('html5lib.treebuilders.etree',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\treebuilders\\etree.py',
   'PYMODULE'),
  ('html5lib.treebuilders.etree_lxml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\treebuilders\\etree_lxml.py',
   'PYMODULE'),
  ('html5lib.treewalkers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\treewalkers\\__init__.py',
   'PYMODULE'),
  ('html5lib.treewalkers.base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\treewalkers\\base.py',
   'PYMODULE'),
  ('html5lib.treewalkers.dom',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\treewalkers\\dom.py',
   'PYMODULE'),
  ('html5lib.treewalkers.etree',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\treewalkers\\etree.py',
   'PYMODULE'),
  ('html5lib.treewalkers.etree_lxml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\treewalkers\\etree_lxml.py',
   'PYMODULE'),
  ('html5lib.treewalkers.genshi',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\html5lib\\treewalkers\\genshi.py',
   'PYMODULE'),
  ('http', 'c:\\programdata\\anaconda3\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'c:\\programdata\\anaconda3\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'c:\\programdata\\anaconda3\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'c:\\programdata\\anaconda3\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'c:\\programdata\\anaconda3\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp', 'c:\\programdata\\anaconda3\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'c:\\programdata\\anaconda3\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\programdata\\anaconda3\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\programdata\\anaconda3\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\programdata\\anaconda3\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\programdata\\anaconda3\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\programdata\\anaconda3\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'c:\\programdata\\anaconda3\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\programdata\\anaconda3\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect', 'c:\\programdata\\anaconda3\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'c:\\programdata\\anaconda3\\lib\\ipaddress.py', 'PYMODULE'),
  ('jinja2',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'c:\\programdata\\anaconda3\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'c:\\programdata\\anaconda3\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'c:\\programdata\\anaconda3\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\programdata\\anaconda3\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'c:\\programdata\\anaconda3\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'c:\\programdata\\anaconda3\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lxml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'c:\\programdata\\anaconda3\\lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'c:\\programdata\\anaconda3\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\programdata\\anaconda3\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netbios',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('netrc', 'c:\\programdata\\anaconda3\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'c:\\programdata\\anaconda3\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'c:\\programdata\\anaconda3\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat._pep440',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\compat\\_pep440.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('olefile',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\olefile\\__init__.py',
   'PYMODULE'),
  ('olefile.olefile',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\olefile\\olefile.py',
   'PYMODULE'),
  ('opcode', 'c:\\programdata\\anaconda3\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'c:\\programdata\\anaconda3\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pdb', 'c:\\programdata\\anaconda3\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'c:\\programdata\\anaconda3\\lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'c:\\programdata\\anaconda3\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'c:\\programdata\\anaconda3\\lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'c:\\programdata\\anaconda3\\lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'c:\\programdata\\anaconda3\\lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'c:\\programdata\\anaconda3\\lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pygments',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.token',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pyparsing',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyparsing.py',
   'PYMODULE'),
  ('pyperclip',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('pyreadline',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\__init__.py',
   'PYMODULE'),
  ('pyreadline.clipboard',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline.clipboard.ironpython_clipboard',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline.clipboard.no_clipboard',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline.clipboard.win32_clipboard',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline.console',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline.console.ansi',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline.console.console',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\console\\console.py',
   'PYMODULE'),
  ('pyreadline.console.event',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\console\\event.py',
   'PYMODULE'),
  ('pyreadline.console.ironpython_console',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline.error',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\error.py',
   'PYMODULE'),
  ('pyreadline.keysyms',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline.keysyms.common',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline.keysyms.ironpython_keysyms',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline.keysyms.keysyms',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline.keysyms.winconstants',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline.lineeditor',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.history',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.lineobj',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.wordmatcher',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline.logger',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\logger.py',
   'PYMODULE'),
  ('pyreadline.modes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline.modes.basemode',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline.modes.emacs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline.modes.notemacs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline.modes.vi',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline.py3k_compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline.release',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\release.py',
   'PYMODULE'),
  ('pyreadline.rlmain',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\rlmain.py',
   'PYMODULE'),
  ('pyreadline.unicode_helper',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pyreadline\\unicode_helper.py',
   'PYMODULE'),
  ('pythoncom',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pywin',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywintypes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('qtpy',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\qtpy\\__init__.py',
   'PYMODULE'),
  ('qtpy.QtCore',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\qtpy\\QtCore.py',
   'PYMODULE'),
  ('qtpy.QtGui',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\qtpy\\QtGui.py',
   'PYMODULE'),
  ('qtpy.QtWidgets',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\qtpy\\QtWidgets.py',
   'PYMODULE'),
  ('qtpy._patch',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\qtpy\\_patch\\__init__.py',
   'PYMODULE'),
  ('qtpy._patch.qcombobox',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\qtpy\\_patch\\qcombobox.py',
   'PYMODULE'),
  ('qtpy._patch.qheaderview',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\qtpy\\_patch\\qheaderview.py',
   'PYMODULE'),
  ('qtpy._version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\qtpy\\_version.py',
   'PYMODULE'),
  ('queue', 'c:\\programdata\\anaconda3\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'c:\\programdata\\anaconda3\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'c:\\programdata\\anaconda3\\lib\\random.py', 'PYMODULE'),
  ('readline',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('requests',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'c:\\programdata\\anaconda3\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'c:\\programdata\\anaconda3\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'c:\\programdata\\anaconda3\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'c:\\programdata\\anaconda3\\lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._path',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'c:\\programdata\\anaconda3\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'c:\\programdata\\anaconda3\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'c:\\programdata\\anaconda3\\lib\\signal.py', 'PYMODULE'),
  ('site', 'c:\\programdata\\anaconda3\\lib\\site.py', 'PYMODULE'),
  ('six', 'c:\\programdata\\anaconda3\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('smtplib', 'c:\\programdata\\anaconda3\\lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'c:\\programdata\\anaconda3\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'c:\\programdata\\anaconda3\\lib\\socketserver.py',
   'PYMODULE'),
  ('socks',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('soupsieve',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('ssl', 'c:\\programdata\\anaconda3\\lib\\ssl.py', 'PYMODULE'),
  ('string', 'c:\\programdata\\anaconda3\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'c:\\programdata\\anaconda3\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'c:\\programdata\\anaconda3\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'c:\\programdata\\anaconda3\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'c:\\programdata\\anaconda3\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'c:\\programdata\\anaconda3\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'c:\\programdata\\anaconda3\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'c:\\programdata\\anaconda3\\lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'c:\\programdata\\anaconda3\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'c:\\programdata\\anaconda3\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'c:\\programdata\\anaconda3\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'c:\\programdata\\anaconda3\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'c:\\programdata\\anaconda3\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.font',
   'c:\\programdata\\anaconda3\\lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'c:\\programdata\\anaconda3\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'c:\\programdata\\anaconda3\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tracemalloc',
   'c:\\programdata\\anaconda3\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'c:\\programdata\\anaconda3\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib3',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.connection',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname._implementation',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu', 'c:\\programdata\\anaconda3\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'c:\\programdata\\anaconda3\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'c:\\programdata\\anaconda3\\lib\\webbrowser.py', 'PYMODULE'),
  ('webencodings',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\webencodings\\__init__.py',
   'PYMODULE'),
  ('webencodings.labels',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\webencodings\\labels.py',
   'PYMODULE'),
  ('webencodings.x_user_defined',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\webencodings\\x_user_defined.py',
   'PYMODULE'),
  ('win32com',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.shell',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.universal',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('win32traceutil',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win_inet_pton',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win_inet_pton.py',
   'PYMODULE'),
  ('winerror',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'c:\\programdata\\anaconda3\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'c:\\programdata\\anaconda3\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'c:\\programdata\\anaconda3\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'c:\\programdata\\anaconda3\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'c:\\programdata\\anaconda3\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'c:\\programdata\\anaconda3\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'c:\\programdata\\anaconda3\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'c:\\programdata\\anaconda3\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'c:\\programdata\\anaconda3\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\programdata\\anaconda3\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\programdata\\anaconda3\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\programdata\\anaconda3\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\programdata\\anaconda3\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\programdata\\anaconda3\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\programdata\\anaconda3\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\programdata\\anaconda3\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\programdata\\anaconda3\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\programdata\\anaconda3\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\programdata\\anaconda3\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\programdata\\anaconda3\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\programdata\\anaconda3\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\programdata\\anaconda3\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'c:\\programdata\\anaconda3\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'c:\\programdata\\anaconda3\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'c:\\programdata\\anaconda3\\lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('zipfile', 'c:\\programdata\\anaconda3\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'c:\\programdata\\anaconda3\\lib\\zipimport.py', 'PYMODULE'),
  ('zipp',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.compat',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'c:\\programdata\\anaconda3\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE')])
